.section-cta {
  position: relative;
  background-color: rgb(23, 23, 23);
  border-radius: .5em;
  border: .2em solid rgb(35, 35, 35);
  padding: 2.4em;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  font-size: $font-size; //10px

  //Tablet client
  @media (min-width: $md-grid) AND (max-width: calc($lg-grid - 1px)) {
    font-size: 8.5px;
  }

  @media (max-width: calc($md-grid - 1px)) {
    flex-direction: column;
    text-align: center;
  }

  .footer-banner-text {
    .footer-banner-first-title {
      font-size: 2.5em;
      font-weight: 400;
      color: #fff;
      text-transform: uppercase;
      display: block;
      text-align: center;

      @media (max-width: calc($md-grid - 1px)) {
        font-size: 1.6em;
      }
    }
    .footer-banner-second-title {
      font-size: 25px;
      font-weight: 600;
      color: $color-bullet-point;
      text-transform: uppercase;
      line-height: normal;
      display: block;
      text-align: center;

      @media (max-width: calc($md-grid - 1px)) {
        font-size: 2.6em;
      }
    }
  }

  .footer-banner-button {
    min-width: 19.2em;

    .cta-item {
      margin: 0.5em 0;
      .cta-template a{
        font-size: 14px;
        min-width: 230px;
        line-height: 1.5;
        font-weight: 900;
        letter-spacing: 0;
      }
    }

    @media (max-width: calc($md-grid - 1px)) {
      width: 100%;
      max-width: 24em;
      margin: 1.6em 0;
    }

    .footer-banner-terms-link {
      display: block;
      text-align: center;
      color: rgb(113, 113, 113);
      text-decoration: underline;
      margin-top: .8em;
      font-size: 1.3em;
      font-weight: 600;
      &:hover{
        color: #fff;
      }
    }
  }

  .desktop-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5%;

    &.d-none {
      display: none;
    }
  }

  .mobile-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.8em;

    &.d-none {
      display: none;
    }

    .mobile-cta {
      width: 40%;
      height: 7em;
      border: 0.01em solid $footer-grid-icon-border;
      max-width: 24em;

      a {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-align: center;
        overflow: hidden;

        img {
          width: 10em;
          height: auto;
          display: block;
        }
      }
    }
  }
}
