<template data-sly-template.default="${@ properties}">
    <div class="banner-container" data-sly-test="${properties.bannerLeftDesktop && properties.bannerRightDesktop}">
        <sly data-sly-use.leftUrl   ="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.bannerLeftUrl}"/>
        <sly data-sly-use.rightUrl  ="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.bannerRightUrl}"/>
        <sly data-sly-set.leftBlank ="${properties.leftBlank ? '_blank' : ''}" />
        <sly data-sly-set.rightBlank="${properties.rightBlank ? '_blank' : ''}" />

        <sly data-sly-use.processorDesktopLeft  ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerLeftDesktop}"/>
        <sly data-sly-use.processorMobileLeft   ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerLeftMobile}"/>
        <sly data-sly-use.processorDesktopRight ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerRightDesktop}"/>
        <sly data-sly-use.processorMobileRight  ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerRightMobile}"/>

        <sly data-sly-set.webpDesktopLeft   ="${processorDesktopLeft.renditions['webp'] || processorDesktopLeft.renditions['original']}"/>
        <sly data-sly-set.webpMobileLeft    ="${processorMobileLeft.renditions['webp'] || processorMobileLeft.renditions['original']}"/>
        <sly data-sly-set.webpDesktopRight  ="${processorDesktopRight.renditions['webp'] || processorDesktopRight.renditions['original']}"/>
        <sly data-sly-set.webpMobileRight   ="${processorMobileRight.renditions['webp'] || processorMobileRight.renditions['original']}"/>

        <input type="hidden" name="src-left-desktop"    value="${webpDesktopLeft.path}">
        <input type="hidden" name="src-left-mobile"     value="${webpMobileLeft.path}">
        <input type="hidden" name="src-right-desktop"   value="${webpDesktopRight.path}">
        <input type="hidden" name="src-right-mobile"    value="${webpMobileRight.path}">

        <div class="banner-wrapper left">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.bannerLeftScript}" />
            <a onclick="${scriptProcessor.processedScript @ context='unsafe'}" class="banner-link" href="${leftUrl.relativePublishLink}" target="${leftBlank}">
                <img class="banner-img" src="" alt="${properties.bannerLeftAlt}" title="${properties.bannerLeftTitle}" loading="lazy">
            </a>
        </div>

        <div class="banner-wrapper right">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.bannerRightScript}" />
            <a onclick="${scriptProcessor.processedScript @ context='unsafe'}" class="banner-link" href="${rightUrl.relativePublishLink}" target="${rightBlank}">
                <img class="banner-img" src="" alt="${properties.bannerRightAlt}" title="${properties.bannerRightTitle}" loading="lazy">
            </a>
        </div>
    </div>
</template>


<template data-sly-template.amp="${@ properties}">
    <sly data-sly-use.leftUrl       ="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.bannerLeftUrl}"/>
    <sly data-sly-use.rightUrl      ="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.bannerRightUrl}"/>
    <sly data-sly-set.leftBlank     ="${properties.leftBlank ? '_blank' : ''}" />
    <sly data-sly-set.rightBlank    ="${properties.rightBlank ? '_blank' : ''}" />

    <sly data-sly-use.processorMobileLeft   ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerLeftMobile}"/>
    <sly data-sly-use.processorMobileRight  ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerRightMobile}"/>

    <sly data-sly-set.webpMobileLeft    ="${processorMobileLeft.renditions['webp'] || processorMobileLeft.renditions['original']}"/>
    <sly data-sly-set.webpMobileRight   ="${processorMobileRight.renditions['webp'] || processorMobileRight.renditions['original']}"/>

    <div class="banner-container" data-sly-test="${properties.bannerLeftMobile || properties.bannerRightMobile}">

        <div class="banner-wrapper left">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.bannerLeftScript}" />
            <a onclick="${scriptProcessor.processedScript @ context='unsafe'}" class="banner-link" href="${leftUrl.relativePublishLink}" target="${leftBlank}">
                <amp-img class="banner-img" src="${webpMobileLeft.path}" alt="${properties.bannerLeftAlt}" loading="lazy" layout="fixed" width="100" height="120"></amp-img>
            </a>
        </div>

        <div class="banner-wrapper right">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.bannerRightScript}" />
            <a onclick="${scriptProcessor.processedScript @ context='unsafe'}" class="banner-link" href="${rightUrl.relativePublishLink}" target="${rightBlank}">
                <amp-img class="banner-img" src="${webpMobileRight.path}" alt="${properties.bannerRightAlt}" loading="lazy" layout="fixed" width="100" height="120"></amp-img>
            </a>
        </div>
    </div>
</template>
