/*~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 ~ Copyright 2020 Adobe Systems Incorporated
 ~
 ~ Licensed under the Apache License, Version 2.0 (the "License");
 ~ you may not use this file except in compliance with the License.
 ~ You may obtain a copy of the License at
 ~
 ~     http://www.apache.org/licenses/LICENSE-2.0
 ~
 ~ Unless required by applicable law or agreed to in writing, software
 ~ distributed under the License is distributed on an "AS IS" BASIS,
 ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ~ See the License for the specific language governing permissions and
 ~ limitations under the License.
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

const path = require('path');

const BUILD_DIR = path.join(__dirname, 'dist');
const CLIENTLIB_DIR = path.join(
  __dirname,
  '..',
  'ui.apps',
  'src',
  'main',
  'content',
  'jcr_root',
  'apps',
  'holdings888',
  'clientlibs'
);

const libsBaseConfig = {
  allowProxy: true,
  serializationFormat: 'xml',
  cssProcessor: ['default:none', 'min:none'],
  jsProcessor: ['default:none', 'min:none']
};

// Config for `aem-clientlib-generator`
module.exports = {
  context: BUILD_DIR,
  clientLibRoot: CLIENTLIB_DIR,
  libs: [

    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino',
      categories: ['holdings888.casino'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-seo-content-container',
      categories: ['holdings888.casino-seo-content-container'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-seo-content-container',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-seo-content-container',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-seo-content-container',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-header-bar-light-lp',
      categories: ['holdings888.casino-header-bar-light-lp'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-header-bar-light-lp',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-header-bar-light-lp',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-header-bar-light-lp',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-header-bar-live-lp',
      categories: ['holdings888.casino-header-bar-live-lp'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-header-bar-live-lp',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-header-bar-live-lp',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-header-bar-live -lp',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-breadcrumb',
      categories: ['holdings888.casino-breadcrumb'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-breadcrumb',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-breadcrumb',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-breadcrumb',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-back-to-top',
      categories: ['holdings888.casino-back-to-top'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-back-to-top',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-back-to-top',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-back-to-top',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-article-slider',
      categories: ['holdings888.casino-article-slider'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-article-slider',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-article-slider',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-article-slider',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-promotions-top-games-carousel',
      categories: ['holdings888.casino-promotions-top-games-carousel'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-promotions-top-games-carousel',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-promotions-top-games-carousel',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-promotions-top-games-carousel',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-banner-disclaimer',
      categories: ['holdings888.casino-banner-disclaimer'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-banner-disclaimer',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-banner-disclaimer',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-banner-disclaimer',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-footer-v2',
      categories: ['holdings888.casino-footer-v2'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-footer-v2',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-footer-v2',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-footer-v2',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-hybrid-footer',
      categories: ['holdings888.casino-hybrid-footer'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-hybrid-footer',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-hybrid-footer',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-hybrid-footer',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-claim-banner',
      categories: ['holdings888.casino-claim-banner'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-claim-banner',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-claim-banner',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-claim-banner',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-top-section',
      categories: ['holdings888.casino-top-section'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-top-section',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-top-section',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-top-section',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-content-banner',
      categories: ['holdings888.casino-content-banner'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-content-banner',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-content-banner',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-content-banner',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-content-banner-slider',
      categories: ['holdings888.casino-content-banner-slider'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-content-banner-slider',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-content-banner-slider',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-content-banner-slider',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },

    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-top-banner',
      categories: ['holdings888.casino-top-banner'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-top-banner',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-top-banner',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-top-banner',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-top-banner-slider',
      categories: ['holdings888.casino-top-banner-slider'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-top-banner-slider',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-top-banner-slider',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-top-banner-slider',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-promotion-teaser',
      categories: ['holdings888.casino-promotion-teaser'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-promotion-teaser',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-promotion-teaser',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-promotion-teaser',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-related-articles-card-slider',
      categories: ['holdings888.casino-related-articles-card-slider'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-related-articles-card-slider',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-related-articles-card-slider',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-related-articles-card-slider',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    },
    {
      ...libsBaseConfig,
      name: 'casino/clientlib-casino-hero-banner-live-lp',
      categories: ['holdings888.clientlib-casino-hero-banner-live-lp'],
      dependencies: [],
      assets: {
        // Copy entrypoint scripts and stylesheets into the respective ClientLib
        // directories
        js: {
          cwd: 'clientlib-casino-hero-banner-live-lp',
          files: ['**/*.js'],
          flatten: false
        },
        css: {
          cwd: 'clientlib-casino-hero-banner-live-lp',
          files: ['**/*.css'],
          flatten: false
        },

        // Copy all other files into the `resources` ClientLib directory
        resources: {
          cwd: 'clientlib-casino-hero-banner-live-lp',
          files: ['**/*.*'],
          flatten: false,
          ignore: ['**/*.js', '**/*.css']
        }
      }
    }
  ]
};
