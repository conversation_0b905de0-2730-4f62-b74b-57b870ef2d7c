<template data-sly-template.default="${@ properties, ctaItems}">
    <sly data-sly-use.ctaLinkTemplate="holdings888/components/common888/htl-templates/cta-link-template.html"/>

    <div data-sly-test="${ctaItems}">
        <sly data-sly-list.cta="${ctaItems}">
            <div class="cta-item">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=cta.properties.ctaUrl, referPath=cta.path}"/>
                <sly data-sly-call="${ctaLinkTemplate.default @
                                    label       = cta.properties.ctaLabel,
                                    ariaLabel   = cta.properties.ctaAriaLabel,
                                    url         = ctaLink.relativePublishLink,
                                    newWindow   = cta.properties.ctaNewWindow,
                                    ctaType     = cta.properties.ctaType,
                                    ctaScript   = cta.properties.ctaScript,
                                    ctaAddsCut  = cta.ctaLink.addsCut,
                                    properties  = cta.properties}"/>
            </div>
        </sly>
    </div>
</template>
