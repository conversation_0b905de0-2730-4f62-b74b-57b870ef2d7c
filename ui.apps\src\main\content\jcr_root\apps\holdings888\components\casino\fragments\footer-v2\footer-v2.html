<sly data-sly-use.paymentsRegulations="footer-payments-and-regulations.html" />
<sly data-sly-use.banner="footer-banner.html" />
<sly data-sly-use.sectionCta="section-cta.html" />
<sly
  data-sly-use.picture="holdings888/components/common888/htl-templates/picture-template.html"
/>
<sly
  data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"
  data-sly-call="${clientlib.css @ categories='holdings888.casino-footer-v2'}"
/>
<sly
  data-sly-use.clientlibs="holdings888/components/common888/htl-templates/fe-clientlibs.html"
/>

<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html" />
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}" />

<sly
  data-sly-use.socialIconItems="${'holdings888/utils/multifield.js' @ multifieldName='socialIcons'}"
/>
<sly
  data-sly-use.ctaItems="${'holdings888/utils/multifield.js' @ multifieldName='ctaItems'}"
/>
<sly
  data-sly-use.egrAwards="${'holdings888/utils/multifield.js' @ multifieldName='egrAwardItems'}"
/>
<sly
  data-sly-use.otherAwards="${'holdings888/utils/multifield.js' @ multifieldName='otherAwardItems'}"
/>
<sly
  data-sly-use.payments="${'holdings888/utils/multifield.js' @ multifieldName='paymentItems'}"
/>
<sly
  data-sly-use.regulations="${'holdings888/utils/multifield.js' @ multifieldName='regulationItems'}"
/>

<sly
  data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"
/>

<div data-mbox-id="${properties.mboxId}">
  <sly data-sly-call="${clientlibs.fe @ locations='casino-footer-v2-css'}" />
  <footer>
    <div class="footer-component-v2">
      <div class="footer-container" automation="footer">
        <div
          class="section section-cta"
          data-sly-test="${properties.mainTitle || properties.subTitle || ctaItems}"
        >
          <div class="footer-banner-text">
            <span class="footer-banner-first-title"
              >${properties.mainTitle}</span
            >
            <span class="footer-banner-second-title"
              >${properties.subTitle}</span
            >
          </div>

          <div class="footer-banner-button">
            <sly
              data-sly-call="${sectionCta.default @ properties=properties, ctaItems=ctaItems}"
            />
            <sly
              data-sly-use.tncLink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='tncLink'}"
            />
            <sly
              data-sly-call="${link.default @
                        properties=tncLink.properties,
                        linkClasses='footer-banner-terms-link footer-link-banner-terms',
                        linkUrlName='tncLink',
                        linkLabelName='tnclinkLabel'
                        }"
            />
          </div>
        </div>

        <div class="section section-top">
          <div
            data-sly-resource="${'footer-top' @ resourceType='holdings888/components/casino/fragments/footer-top', decorationTagName = 'div'}"
            data-sly-unwrap="${!wcmmode.edit}"
          ></div>
        </div>

        <div
          class="section section-awards"
          data-sly-test="${egrAwards || otherAwards}"
        >
          <div class="award-sub-section egr-awards">
            <sly data-sly-list.egrAward="${egrAwards}">
              <sly
                data-sly-call="${picture.basic @ imagePath=egrAward.properties.egrAwardLogo,alt=egrAward.properties.egrAwardAlt,title=egrAward.properties.egrAwardImgTitle,cssClassNames='logo'}"
              />
            </sly>
          </div>

          <div class="award-sub-section other-awards">
            <sly data-sly-list.otherAward="${otherAwards}">
              <sly
                data-sly-set.otherAwardProperties="${otherAward.properties}"
              />
              <sly
                data-sly-call="${link.default @
                                properties=otherAwardProperties,
                                imagePath=otherAwardProperties.otherAwardLogo,
                                imageAlt=otherAwardProperties.otherAwardAlt,
                                imageTitle=otherAwardProperties.otherAwardImgTitle,
                                imageClasses='logo'
                                }"
              />
            </sly>
          </div>

          <div class="separator"></div>
        </div>

        <div
          class="section section-grid"
          data-sly-test="${payments || regulations}"
        >
          <sly
            data-sly-call="${paymentsRegulations.default @ properties=properties, payments=payments, regulations=regulations}"
          />
        </div>

        <div class="section section-banner">
          <sly data-sly-call="${banner.default @ properties=properties}" />
        </div>
        <sly
          data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text = properties.text}"
        />
        <div class="section section-disclaimer">
          ${richText.text @ context='html'}
        </div>

        <div class="section section-hidden"></div>
      </div>
    </div>
  </footer>
  <sly
    data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"
    data-sly-call="${clientlib.js @ categories='holdings888.casino-footer-v2'}"
  />

  <sly data-sly-call="${clientlibs.fe @ locations='casino-footer-v2-js'}" />
</div>
