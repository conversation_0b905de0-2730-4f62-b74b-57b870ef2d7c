<template data-sly-template.default="${@ properties}">
    <div class="banner-container" data-sly-test="${properties.bannerLeftDesktop && properties.bannerRightDesktop}">
        <sly data-sly-use.imageLink="holdings888/components/common888/htl-templates/link-template.html"/>

        <sly data-sly-use.processorDesktopLeft  ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerLeftDesktop}"/>
        <sly data-sly-use.processorMobileLeft   ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerLeftMobile}"/>
        <sly data-sly-use.processorDesktopRight ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerRightDesktop}"/>
        <sly data-sly-use.processorMobileRight  ="${'holdings888.core.models.ImageRenditionProcessor' @ pathToProcess=properties.bannerRightMobile}"/>

        <sly data-sly-set.webpDesktopLeft   ="${processorDesktopLeft.renditions['webp'] || processorDesktopLeft.renditions['original']}"/>
        <sly data-sly-set.webpMobileLeft    ="${processorMobileLeft.renditions['webp'] || processorMobileLeft.renditions['original']}"/>
        <sly data-sly-set.webpDesktopRight  ="${processorDesktopRight.renditions['webp'] || processorDesktopRight.renditions['original']}"/>
        <sly data-sly-set.webpMobileRight   ="${processorMobileRight.renditions['webp'] || processorMobileRight.renditions['original']}"/>

        <input type="hidden" name="src-left-desktop"    value="${webpDesktopLeft.path}">
        <input type="hidden" name="src-left-mobile"     value="${webpMobileLeft.path}">
        <input type="hidden" name="src-right-desktop"   value="${webpDesktopRight.path}">
        <input type="hidden" name="src-right-mobile"    value="${webpMobileRight.path}">

        <sly data-sly-use.bannerLeftlink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='bannerLeftlink'}" />
        <div class="banner-wrapper left">
            <sly data-sly-call="${imageLink.default @
                        properties=bannerLeftlink.properties,
                        linkClasses='banner-link',
                        linkUrlName='bannerLeftUrl',
                        legacyNewTab=properties.leftBlank,
                        scriptName='bannerLeftScript',
                        imagePath=properties.bannerLeftDesktop,
                        imageAlt=properties.bannerLeftAlt,
                        imageTitle=properties.bannerLeftTitle,
                        imageClasses='banner-img'}"/>
        </div>

        <sly data-sly-use.bannerRightlink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='bannerRightlink'}" />
        <div class="banner-wrapper right">
            <sly data-sly-call="${imageLink.default @
                        properties=bannerRightlink.properties,
                        linkClasses='banner-link',
                        linkUrlName='bannerRightUrl',
                        legacyNewTab=properties.rightBlank,
                        scriptName='bannerRightScript',
                        imagePath=properties.bannerRightDesktop,
                        imageAlt=properties.bannerRightAlt,
                        imageTitle=properties.bannerRightTitle,
                        imageClasses='banner-img'}"/>
        </div>
    </div>
</template>