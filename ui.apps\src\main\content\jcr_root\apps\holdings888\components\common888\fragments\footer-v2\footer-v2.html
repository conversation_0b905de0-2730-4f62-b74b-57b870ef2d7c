<sly data-sly-use.paymentsRegulations="footer-payments-and-regulations.html"/>
<sly data-sly-use.banner="footer-banner.html"/>
<sly data-sly-use.sectionCta="section-cta.html"/>
<sly data-sly-use.picture="holdings888/components/common888/htl-templates/picture-template.html"/>

<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<sly data-sly-use.socialIconItems="${'holdings888/utils/multifield.js' @ multifieldName='socialIcons'}"/>
<sly data-sly-use.ctaItems          ="${'holdings888/utils/multifield.js' @ multifieldName='ctaItems'}"/>
<sly data-sly-use.egrAwards         ="${'holdings888/utils/multifield.js' @ multifieldName='egrAwardItems'}"/>
<sly data-sly-use.otherAwards       ="${'holdings888/utils/multifield.js' @ multifieldName='otherAwardItems'}"/>
<sly data-sly-use.payments          ="${'holdings888/utils/multifield.js' @ multifieldName='paymentItems'}"/>
<sly data-sly-use.regulations       ="${'holdings888/utils/multifield.js' @ multifieldName='regulationItems'}"/>

<div data-mbox-id="${properties.mboxId}">
    <footer>
        <div class="footer-component-v2">
            <div class="footer-container">
                <div class="section section-top" >
                    <div data-sly-resource="${'footer-top' @ resourceType='holdings888/components/fragments/footer-top', decorationTagName = 'div'}"
                         data-sly-unwrap="${!wcmmode.edit}"></div>
                </div>

                <div class="section section-cta" ><sly data-sly-call="${sectionCta.default @ properties=properties, ctaItems=ctaItems}"/></div>

                <div class="section section-awards" data-sly-test="${egrAwards || otherAwards}">
                    <div class="award-sub-section egr-awards">
                        <sly data-sly-list.egrAward="${egrAwards}">
                            <sly data-sly-call="${picture.basic @ imagePath=egrAward.properties.egrAwardLogo,alt=egrAward.properties.egrAwardAlt,title=egrAward.properties.egrAwardImgTitle,loading='lazy',cssClassNames='logo'}"/>
                        </sly>
                    </div>

                    <div class="award-sub-section other-awards">
                        <sly data-sly-list.otherAward="${otherAwards}">
                            <sly data-sly-call="${picture.basic @ imagePath=otherAward.properties.otherAwardLogo,alt=otherAward.properties.otherAwardAlt,title=otherAward.properties.otherAwardImgTitle,loading='lazy',cssClassNames='logo'}"/>
                        </sly>
                    </div>

                    <div class="separator"></div>
                </div>

                <div class="section section-grid" data-sly-test="${payments || regulations}"><sly data-sly-call="${paymentsRegulations.default @ properties=properties, payments=payments, regulations=regulations}"/></div>

                <div class="section section-banner"><sly data-sly-call="${banner.default @ properties=properties}"/></div>
                <sly data-sly-use.richText="${'holdings888.core.models.RichTextImp' @ text = properties.text}" />
                <div class="section section-disclaimer">${richText.text @ context='html'}</div>
            </div>
        </div>
    </footer>
</div>


