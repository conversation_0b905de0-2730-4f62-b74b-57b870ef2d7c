<sly data-sly-use.ctaItems="${'holdings888/utils/multifield.js' @ multifieldName='ctaItems'}" />
<sly data-sly-use.tncLink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='tncLink'}" />
<sly data-sly-test.hasContent="${properties.mainTitle || properties.subTitle || ctaItems || tncLink}" />

<!--/* Pure JSON output for .json selector */-->
<sly data-sly-test="${hasContent}">
{
  "mainTitle": "${properties.mainTitle @ context='text'}",
  "subTitle": "${properties.subTitle @ context='text'}",
  "ctaItems": [<sly data-sly-list.cta="${ctaItems}">
    <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=cta.properties.ctaUrl, referPath=cta.path}"/>
    {
      "label": "${cta.properties.ctaLabel @ context='text'}",
      "ariaLabel": "${cta.properties.ctaAriaLabel @ context='text'}",
      "url": "${ctaLink.relativePublishLink @ context='text'}",
      "ctaType": "${cta.properties.ctaType @ context='text'}",
      "newWindow": ${cta.properties.ctaNewWindow @ context='text'},
      "script": "${cta.properties.ctaScript @ context='text'}"
    }<sly data-sly-test="${!ctaLast}">,</sly></sly>
  ],
  "tncLink": <sly data-sly-test="${tncLink}">{
    "url": "${tncLink.properties.tncLink @ context='text'}",
    "label": "${tncLink.properties.tnclinkLabel @ context='text'}"
  }</sly><sly data-sly-test="${!tncLink}">null</sly>
}
</sly>

<!--/* Empty object if no content */-->
<sly data-sly-test="${!hasContent}">{}</sly>
