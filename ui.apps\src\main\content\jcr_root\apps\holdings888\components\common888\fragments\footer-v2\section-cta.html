<template data-sly-template.default="${@ properties, ctaItems}">
    <sly data-sly-use.ctaTemplate="holdings888/components/editorial/htl-templates/cta-template.html"/>
    <sly data-sly-use.picture="holdings888/components/common888/htl-templates/picture-template.html"/>

    <div class="desktop-wrapper" data-sly-test="${ctaItems}">
        <sly data-sly-list.cta="${ctaItems}">
            <div class="cta-item">
                <sly data-sly-use.ctaLink="${'holdings888.core.models.LinkModel' @ urlToProcess=cta.properties.ctaUrl, referPath=cta.path}"/>
                <sly data-sly-call="${ctaTemplate.default @
                                    label       = cta.properties.ctaLabel,
                                    ariaLabel   = cta.properties.ctaAriaLabel,
                                    url         = ctaLink.relativePublishLink,
                                    newWindow   = cta.properties.ctaNewWindow,
                                    ctaType     = cta.properties.ctaType,
                                    ctaScript   = cta.properties.ctaScript,
                                    ctaAddsCut  = cta.ctaLink.addsCut}"/>
            </div>
        </sly>
    </div>

    <div class="mobile-wrapper" data-sly-test="${properties.appleStoreIcon || properties.googlePlayIcon}">
        <div class="mobile-cta apple-store">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.appleStoreScript}" />
            <sly data-sly-use.appleUrl="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.appleStoreUrl}"/>
            <a href="${appleUrl.relativePublishLink}" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly data-sly-call="${picture.basic @ imagePath=properties.appleStoreIcon,alt=properties.appleStoreAlt}"/>
            </a>
        </div>

        <div class="mobile-cta google-play">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.googlePlayScript}" />
            <sly data-sly-use.googleUrl="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.googlePlayUrl}"/>
            <a href="${googleUrl.relativePublishLink}" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <sly data-sly-call="${picture.basic @ imagePath=properties.googlePlayIcon,alt=properties.googlePlayAlt}"/>
            </a>
        </div>
    </div>
</template>


<template data-sly-template.amp="${@ properties,ctaItems}">
    <sly data-sly-use.ctaTemplate="holdings888/components/editorial/htl-templates/cta-template.html"/>

    <div class="mobile-wrapper" data-sly-test="${properties.appleStoreIcon || properties.googlePlayIcon}">
        <div class="mobile-cta apple-store">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.appleStoreScript}" />
            <sly data-sly-use.appleUrl="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.appleStoreUrl}"/>

            <a href="${appleUrl.relativePublishLink}" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <amp-img src="${properties.appleStoreIcon}" alt="${properties.appleStoreAlt}" layout="fixed" width="100" height="50"></amp-img>
            </a>
        </div>

        <div class="mobile-cta google-play">
            <sly data-sly-use.scriptProcessor="${'holdings888.core.models.ScriptProcessor' @ scriptToProcess=properties.googlePlayScript}" />
            <sly data-sly-use.googleUrl="${'holdings888.core.models.LinkModel' @ urlToProcess=properties.googlePlayUrl}"/>

            <a href="${googleUrl.relativePublishLink}" onclick="${scriptProcessor.processedScript @ context='unsafe'}">
                <amp-img src="${properties.googlePlayIcon}" alt="${properties.googlePlayAlt}" layout="fixed" width="100" height="50"></amp-img>
            </a>
        </div>
    </div>
</template>