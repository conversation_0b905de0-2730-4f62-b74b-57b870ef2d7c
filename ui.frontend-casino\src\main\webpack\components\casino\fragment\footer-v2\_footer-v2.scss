footer {
  background: $background-color-footer;
  color: $color-font-footer;
  margin: 0 auto;
  font-size: $font-size; //10px

  //Tablet client
  @media (min-width: $md-grid) AND (max-width: calc($lg-grid - 1px)) {
    font-size: 8.5px;
  }
  
  .footer-component-v2 {
    margin: 0 auto;
    width: 100%;
    padding: 3em 5em 2em;

    @media (max-width: calc($sm-grid - 1px)) {
      padding: 3em 2em 2em;
    }

    @media (min-width: $sm-grid) AND (max-width: calc($md-grid - 1px)) {
      padding: 1em 2em 2em;
    }

    .section-cta {
      position: relative;
      background-color: rgb(23, 23, 23);
      border-radius: .5em;
      border: .2em solid rgb(35, 35, 35);
      padding: 2.4em;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      align-items: center;

      @media (max-width: calc($md-grid - 1px)) {
        flex-direction: column;
        text-align: center;
      }

      .footer-banner-text {
        .footer-banner-first-title {
          font-size: 2.5em;
          font-weight: 400;
          color: #fff;
          text-transform: uppercase;
          display: block;
          text-align: center;

          @media (max-width: calc($md-grid - 1px)) {
            font-size: 1.6em;
          }
        }
        .footer-banner-second-title {
          font-size: 25px;
          font-weight: 600;
          color: $color-bullet-point;
          text-transform: uppercase;
          line-height: normal;
          display: block;
          text-align: center;

          @media (max-width: calc($md-grid - 1px)) {
            font-size: 2.6em;
          }
        }
      }

      .footer-banner-button {
        min-width: 19.2em;

        .cta-item {
          margin: 0.5em 0;
          .cta-template a{
            font-size: 14px;
            min-width: 230px;
            line-height: 1.5;
            font-weight: 900;
            letter-spacing: 0;
          }
        }

        @media (max-width: calc($md-grid - 1px)) {
          width: 100%;
          max-width: 24em;
          margin: 1.6em 0;
        }

      }

      .footer-banner-terms-link {
        display: block;
        text-align: center;
        color: rgb(113, 113, 113);
        text-decoration: underline;
        margin-top: .8em;
        font-size: 1.3em;
        font-weight: 600;
        &:hover{
          color: #fff;
        }
      }

      .desktop-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5%;

        &.d-none {
          display: none;
        }
      }

      .mobile-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.8em;

        &.d-none {
          display: none;
        }

        .mobile-cta {
          width: 40%;
          height: 7em;
          border: 0.01em solid $footer-grid-icon-border;
          max-width: 24em;

          a {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
            overflow: hidden;

            img {
              width: 10em;
              height: auto;
              display: block;
            }
          }
        }
      }
    }

    .section-awards {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      column-gap: 0.8em;
      row-gap: 1.6em;
      margin-top: 3.2em;
      margin-bottom: 0;

      @media (min-width: calc($md-grid - 1px)) {
        margin-bottom: 4em;
      }

      .award-sub-section {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.8em;
        width: 100%;

        @media (min-width: calc($md-grid - 1px)) {
          width: auto;
        }

        .logo {
          height: 6em;
          width: auto;
          padding: 0  1.6em;
        }

        &.other-awards {
          .logo {
            height: auto;
            max-width: 21.6em;
          }
        }
      }
    }

    .section-grid {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0;
      padding: 2em 3em 0;

      .grid-sub-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 1em 3%;
        grid-auto-rows: max-content;
        gap: 0.8em;
        width: 100%;
        padding-bottom: 2em;

        &.payments {
          .grid-item {
            //align last even item at center of grid
            //task 13641 requires full width for last even item
            @media (max-width: calc($md-grid - 1px)) {
              &:last-child:nth-child(2n - 1) {
                width: 100%;                                                         
                justify-self: center;
                grid-column-end: span 2;
              }
            }
          }
        }

        &.regulations {
          .grid-item {
            //align last even item at center of grid
            @media (max-width: calc($md-grid - 1px)) {
              &:last-child:nth-child(2n - 1) {
                width: 100%;
                justify-self: center;
                grid-column-end: span 2;
              }
            }
          }
        }

        @media (min-width: $sm-grid) {
          width: 50%;
          padding-bottom: 4em;

          &.payments {
            border-right: 0.02em solid $footer-separator;
            padding-right: 4.8em;
          }

          &.regulations {
            padding-left: 4.8em;
          }
        }

        @media (min-width: $md-grid) {
          grid-template-columns: 1fr 1fr 1fr;
        }

        .grid-item {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 0.1em solid $footer-grid-icon-border;
          text-decoration: none;
          height: 50px;
          padding: 0 4px;         

          //align last even item at center of grid
          @media (max-width: calc($sm-grid - 1px)) {
            &:last-child:nth-child(2n - 1) {
              justify-self: center;
              grid-column-end: span 2;
              width: 100%;
            }
          }

          .logo {
            height: 40px;
            width: 100%;
            object-fit: contain;
            display: flex;
            margin: 0 auto;
          }
        }
      }

      @media (max-width: calc($sm-grid - 1px)) {
        margin-bottom: 4.8em;
        padding: 0 1em 0;
      }

      @media (min-width: $sm-grid) AND (max-width: calc($md-grid - 1px)) {
        border-top: .1em solid $footer-separator;
        padding: 2em 0;
      }
    }

    .section-banner {
      margin: 2.4em 0 3.2em;

      .banner-container {
        position: relative;
        background-color: $footer-banner-background;
        width: 99%;
        margin: 0 auto;
        height: 8em;
        border-radius: 1em;

        .banner-wrapper {
          height: 100%;
          width: auto;
          position: absolute;

          .banner-img,
          .banner-link {
            display: block;
            height: 100%;
            width: auto;
            max-width: 100%;
          }

          &.left {
            left: 0;
            max-width: 70%;

            @media (min-width: calc($md-grid - 1px)) {
              max-width: 60%;
            }
          }

          &.right {
            right: 0;
            max-width: 30%;

            @media (min-width: calc($md-grid - 1px)) {
              max-width: 40%;
            }
          }
        }
      }
    }

    .section-disclaimer {
      margin-bottom: 2em;
      padding-top: 3em;
      border-top: .1em solid $footer-separator;

      p {
        color: $gray4;
        font-size: 1.2em;
        line-height: 1.6;
        
        a {
          color: $color-font-footer;
          text-decoration: underline;
        }
      }

      @media (min-width: $sm-grid) AND (max-width: calc($lg-grid - 1px)) {
        border-top: none;
      }
    }

    .separator {
      width: 100%;
      padding: 0 0 2em;

      &:after {
        content: "";
        display: block;
        border: 0.1em solid $footer-separator;
      }

      @media (min-width: $sm-grid) {
        display: none;
      }

      &.after-grid {
        display: none;
      }
    }

    .gray-filter {
      filter: brightness(0) invert(0.7);
      @media (min-width: $md-grid) {
        filter: brightness(0) invert(0.4);
      }
      &:hover {
        filter: none;
        transition: all 0.3s ease-in-out;
      }
    }

    .gray-filter-1 {
      filter: brightness(0.3) invert(0.2);
      &:hover {
        filter: none;
        transition: all 0.3s ease-in-out;
      }
    }
    .gray-filter-2 {
      filter: brightness(1.2) invert(0.8);
      &:hover {
        filter: none;
        transition: all 0.3s ease-in-out;
      }
    }
    .gray-filter-3 {
      filter: brightness(1) invert(0.5);
      &:hover {
        filter: none;
        transition: all 0.3s ease-in-out;
      }
    }
    .gray-filter-4 {
      filter: brightness(0.5) grayscale(1);
      &:hover {
        filter: grayscale(1);
        transition: all 0.3s ease-in-out;
      }
    }
  }
}
