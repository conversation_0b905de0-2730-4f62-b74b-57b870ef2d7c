'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TSConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const resolve = {
    extensions: ['.js', '.ts'],
    plugins: [new TSConfigPathsPlugin({
        configFile: './tsconfig.json'
    })]
};

module.exports = {
    context: path.resolve(__dirname, './src/main/webpack'),
    resolve: resolve,
    entry: {
        "casino" : './casino/main.ts',
        'casino-top-section' : './casino/main-top-section.ts',
        'casino-seo-content-container' : './components/casino/fragment/seo-content-container/seo-content-container.ts',
        'casino-breadcrumb' : './components/casino/fragment/breadcrumb/breadcrumb.ts',
        'casino-content-banner' : './components/casino/fragment/content-banner-slider/content-banner/content-banner.ts',
        'casino-content-banner-slider' : './components/casino/fragment/content-banner-slider/content-banner-slider.ts',
        'casino-top-banner-slider' : './components/casino/fragment/top-banner-slider/top-banner-slider.ts',
        'casino-top-banner' : './components/casino/fragment/top-banner-slider/top-banner/top-banner.ts',
        'casino-footer-v2' : './components/casino/fragment/footer-v2/footer-v2.ts',
        'casino-hybrid-footer' : './components/casino/fragment/hybrid-footer/hybrid-footer.ts',
        'casino-claim-banner' : './components/casino/fragment/claim-banner/claim-banner.ts',
        'casino-back-to-top' : './components/casino/fragment/back-to-top/back-to-top.ts',
        'casino-article-slider' : './components/casino/fragment/article-slider/article-slider.ts',
        'casino-promotions-top-games-carousel' : './components/casino/fragment/promotions-top-games-carousel/promotions-top-games-carousel.ts',
        'casino-banner-disclaimer' : './components/casino/fragment/banner-disclaimer/banner-disclaimer.ts',
        'casino-promotion-teaser' : './components/casino/fragment/promotion-teaser/promotion-teaser.ts',
        'casino-header-bar-light-lp' : './components/casino/fragment/header-bar-light-lp/header-bar-light-lp.ts',
        'casino-header-bar-live-lp' : './components/casino/fragment/header-bar-live-lp/header-bar-live-lp.ts',
        'casino-promotions-carousel' : './components/casino/fragment/promotions-carousel/promotions-carousel.ts',
        'casino-related-articles-card-slider' : './components/casino/fragment/related-articles-card-slider/related-articles-card-slider.ts',
        'casino-header-bar-uk-lp' : './components/casino/fragment/header-bar-uk-lp/header-bar-uk-lp.ts',
        "casino-content-banner-homepage": "/components/casino/fragment/content-banner-homepage/content-banner-homepage.ts",
        "casino-hero-banner-live-lp": "/components/casino/fragment/hero-banner-live-lp/hero-banner-live-lp.ts"

    },
    optimization: {
        minimize:false,
        splitChunks: {
            chunks: 'all', // The option could be set to 'async', so all vendors libs will be included in EACH component
            // file, but it's better to have them in separate files
            cacheGroups: {
                // "This chunk contains all the external libraries used in the project"
                defaultVendors: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'casino-vendors', // The chunk should be the same as location name in HtmlPageItemsConfig
                    chunks: 'all',
                }
            }
        }
     },
    output: {
       filename: 'aem-static/js/[name].js',
       path: path.resolve(__dirname, 'dist')
   },
   module: {
       rules: [
           {
               test: /\.tsx?$/,
               exclude: /node_modules/,
               use: [
                   {
                       loader: 'ts-loader'
                   },
                   {
                       loader: 'glob-import-loader',
                       options: {
                           resolve: resolve
                       }
                   }
               ]
           },
           {
               test: /\.scss$/,
               use: [
                   MiniCssExtractPlugin.loader,
                   {
                       loader: 'css-loader',
                       options: {
                           url: false
                       }
                   },
                   {
                       loader: 'sass-loader',
                   },
                   {
                       loader: 'glob-import-loader',
                       options: {
                           resolve: resolve
                       }
                   }
               ]
           },
           {
               test: /\.(ico|jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)(\?.*)?$/,
               use: {
                 loader: 'file-loader',
                 options: {
                   name: '[path][name].[ext]'
                 }
               }
           },
       ]
   },
   plugins: [
       new CleanWebpackPlugin(),
       new ESLintPlugin({
           extensions: ['js', 'ts', 'tsx']
       }),
       new MiniCssExtractPlugin({
           filename: 'aem-static/css/[name].css'
       }),
       new CopyWebpackPlugin({
           patterns: [
             { from: './resources', to: './aem-static/resources' },
           ]
       })
   ],
   stats: {
       assetsSort: 'chunks',
       builtAt: true,
       children: false,
       chunkGroups: true,
       chunkOrigins: true,
       colors: false,
       errors: true,
       errorDetails: true,
       env: true,
       modules: false,
       performance: true,
       providedExports: false,
       source: false,
       warnings: true
   }
};