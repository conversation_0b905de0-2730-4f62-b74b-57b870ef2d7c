<sly data-sly-use.sectionCta="section-cta.html"/>
<sly data-sly-use.link="holdings888/components/common888/htl-templates/link-template.html"/>
<sly data-sly-use.ctaItems="${'holdings888/utils/multifield.js' @ multifieldName='ctaItems'}"/>

<sly data-sly-use.template="core/wcm/components/commons/v1/templates.html"/>
<sly data-sly-call="${template.placeholder @ isEmpty=!hasContent}"/>

<div
  class="section section-cta"
  data-sly-test="${properties.mainTitle || properties.subTitle || ctaItems}"
>
  <div class="footer-banner-text">
    <span class="footer-banner-first-title"
      >${properties.mainTitle}</span
    >
    <span class="footer-banner-second-title"
      >${properties.subTitle}</span
    >
  </div>

  <div class="footer-banner-button">
    <sly
      data-sly-call="${sectionCta.default @ properties=properties, ctaItems=ctaItems}"
    />
    <sly
      data-sly-use.tncLink="${'holdings888/components/common888/utils/childNode.js' @ nodeName='tncLink'}"
    />
    <sly
      data-sly-call="${link.default @
                properties=tncLink.properties,
                linkClasses='footer-banner-terms-link footer-link-banner-terms',
                linkUrlName='tncLink',
                linkLabelName='tnclinkLabel'
                }"
    />
  </div>
</div>
