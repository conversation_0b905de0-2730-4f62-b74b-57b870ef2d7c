'use strict';

const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TSConfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

const SOURCE_ROOT = __dirname + '/src/main/webpack';

const resolve = {
    extensions: ['.js', '.ts'],
    plugins: [new TSConfigPathsPlugin({
        configFile: './tsconfig.json'
    })]
};

module.exports = {
    resolve: resolve,
    entry: {
        casino : SOURCE_ROOT + '/casino/main.ts',
        'casino-top-section' : SOURCE_ROOT + '/casino/main-top-section.ts',
        'casino-seo-content-container' : SOURCE_ROOT + '/components/casino/fragment/seo-content-container/seo-content-container.ts',
        'casino-breadcrumb' : SOURCE_ROOT + '/components/casino/fragment/breadcrumb/breadcrumb.ts',
        'casino-content-banner' : SOURCE_ROOT + '/components/casino/fragment/content-banner-slider/content-banner/content-banner.ts',
        'casino-content-banner-slider' : SOURCE_ROOT + '/components/casino/fragment/content-banner-slider/content-banner-slider.ts',
        'casino-top-banner-slider' : SOURCE_ROOT + '/components/casino/fragment/top-banner-slider/top-banner-slider.ts',
        'casino-offer-banner' : SOURCE_ROOT + '/components/casino/fragment/offer-banner/offer-banner.ts',
        'casino-top-banner' : SOURCE_ROOT + '/components/casino/fragment/top-banner-slider/top-banner/top-banner.ts',
        'casino-footer-v2' : SOURCE_ROOT + '/components/casino/fragment/footer-v2/footer-v2.ts',
        'casino-hybrid-footer' : SOURCE_ROOT + '/components/casino/fragment/hybrid-footer/hybrid-footer.ts',
        'casino-claim-banner' : SOURCE_ROOT + '/components/casino/fragment/claim-banner/claim-banner.ts',
        'casino-back-to-top' : SOURCE_ROOT + '/components/casino/fragment/back-to-top/back-to-top.ts',
        'casino-article-slider' : SOURCE_ROOT + '/components/casino/fragment/article-slider/article-slider.ts',
        'casino-promotions-top-games-carousel' : SOURCE_ROOT + '/components/casino/fragment/promotions-top-games-carousel/promotions-top-games-carousel.ts',
        'casino-banner-disclaimer' : SOURCE_ROOT + '/components/casino/fragment/banner-disclaimer/banner-disclaimer.ts',
        'casino-promotion-teaser' : SOURCE_ROOT + '/components/casino/fragment/promotion-teaser/promotion-teaser.ts',
        'casino-header-bar-light-lp' : SOURCE_ROOT + '/components/casino/fragment/header-bar-light-lp/header-bar-light-lp.ts',
        'casino-header-bar-live-lp' : SOURCE_ROOT + '/components/casino/fragment/header-bar-live-lp/header-bar-live-lp.ts',
        'casino-related-articles-card-slider' : SOURCE_ROOT + '/components/casino/fragment/related-articles-card-slider/related-articles-card-slider.ts',
        'casino-hero-banner-live-lp' : SOURCE_ROOT + '/components/casino/fragment/hero-banner-live-lp/hero-banner-live-lp.ts'



    },
    optimization: {
        minimize:false
    },
    output: {
        filename: (chunkData) => {
            return chunkData.chunk.name === 'dependencies' ? 'clientlib-dependencies/[name].js' : 'clientlib-[name]/[name].js';
        },
        path: path.resolve(__dirname, 'dist')
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'ts-loader'
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve
                        }
                    }
                ]
            },
            {
                test: /\.css$/i,
                use: ["style-loader", "css-loader"],
            },
            {
                test: /\.scss$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            url: false
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            plugins() {
                                return [
                                    require('autoprefixer')
                                ];
                            }
                        }
                    },
                    {
                        loader: 'sass-loader',
                    },
                    {
                        loader: 'glob-import-loader',
                        options: {
                            resolve: resolve
                        }
                    }
                ]
            }
        ]
    },
    plugins: [
        new CleanWebpackPlugin(),
        new ESLintPlugin({
            extensions: ['js', 'ts', 'tsx']
        }),
        new MiniCssExtractPlugin({
            filename: 'clientlib-[name]/[name].css'
        }),
        new CopyWebpackPlugin({
            patterns: [
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-seo-content-container' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-breadcrumb' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-top-section' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-footer-v2' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-hybrid-footer' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-claim-banner' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-content-banner' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-content-banner-slider' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-top-banner-slider' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-offer-banner' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-top-banner' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-back-to-top' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-article-slider' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-promotions-top-games-carousel' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-promotion-teaser' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-header-bar-light-lp' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-related-articles-card-slider' },
                { from: path.resolve(__dirname, SOURCE_ROOT + '/resources/casino'), to: './clientlib-casino-hero-banner-live-lp' },


            ]
        })
    ],
    stats: {
        assetsSort: 'chunks',
        builtAt: true,
        children: false,
        chunkGroups: true,
        chunkOrigins: true,
        colors: false,
        errors: true,
        errorDetails: true,
        env: true,
        modules: false,
        performance: true,
        providedExports: true,
        source: true,
        warnings: true
    }
};
