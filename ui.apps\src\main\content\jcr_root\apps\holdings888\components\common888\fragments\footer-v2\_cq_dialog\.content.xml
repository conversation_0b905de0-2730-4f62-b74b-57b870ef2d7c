<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0" xmlns:granite="http://www.adobe.com/jcr/granite/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:sling="http://sling.apache.org/jcr/sling/1.0"
    jcr:primaryType="nt:unstructured"
    jcr:title="Configuration Footer"
    sling:resourceType="cq/gui/components/authoring/dialog"
    extraClientlibs="[888poker.components.author.editor,holdings888.richtext,rte.dialog.rich-text,holdings888.dialog.altText]">
    <content
        granite:class="cmp-image__editor"
        jcr:primaryType="nt:unstructured"
        sling:resourceType="granite/ui/components/coral/foundation/container">
        <items jcr:primaryType="nt:unstructured">
            <tabs
                jcr:primaryType="nt:unstructured"
                sling:resourceType="granite/ui/components/coral/foundation/tabs"
                maximized="{Boolean}true">
                <items jcr:primaryType="nt:unstructured">
                    <tabCta
                        jcr:primaryType="nt:unstructured"
                        jcr:title="CTA"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <headingDesktopCta
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}3"
                                                text="Desktop CTAs"/>
                                            <ctaItems
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                granite:class="cta-multifield"
                                                validation="minmax-multifield">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    max-items="2"
                                                    min-items="0"
                                                    name="CTA"/>
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./ctaItems">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                            jcr:primaryType="nt:unstructured"
                                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <headingCta
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                                    level="{Long}3"
                                                                    text="CTA"/>
                                                                <ctaType
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/select"
                                                                    emptyOption="{Boolean}false"
                                                                    multiple="{Boolean}false"
                                                                    name="./ctaType">
                                                                    <items jcr:primaryType="nt:unstructured">
                                                                        <glow
                                                                            jcr:primaryType="nt:unstructured"
                                                                            text="CTA Glow (default)"
                                                                            value="cta-glow"/>
                                                                        <primary
                                                                            jcr:primaryType="nt:unstructured"
                                                                            selected="{Boolean}true"
                                                                            text="CTA Primary"
                                                                            value="cta-primary"/>
                                                                        <secondary
                                                                            jcr:primaryType="nt:unstructured"
                                                                            text="CTA Secondary"
                                                                            value="cta-secondary"/>
                                                                        <tertiary
                                                                            jcr:primaryType="nt:unstructured"
                                                                            text="CTA Tertiary"
                                                                            value="cta-tertiary"/>
                                                                        <outline
                                                                            jcr:primaryType="nt:unstructured"
                                                                            text="CTA Outlined"
                                                                            value="cta-outline"/>
                                                                    </items>
                                                                </ctaType>
                                                                <ctaLabel
                                                                    jcr:description="Insert the label of the CTA"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="CTA Label"
                                                                    name="./ctaLabel"
                                                                    required="{Boolean}true"/>
                                                                <ctaAriaLabel
                                                                    jcr:description="Insert the Aria Label of the CTA (Accessibility)"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldDescription="This attribute is used to provide additional information to help clarify or further describe the purpose of a link. Can also be useful to people with disabilities."
                                                                    fieldLabel="ARIA Label (Accessibility)"
                                                                    name="./ctaAriaLabel"
                                                                    required="{Boolean}false"/>
                                                                <ctaUrl
                                                                    jcr:description="Insert the url of the CTA"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                                    fieldLabel="CTA url"
                                                                    name="./ctaUrl"
                                                                    required="{Boolean}true"
                                                                    rootPath="/content"/>
                                                                <queryParameters
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/include"
                                                                    path="holdings888/components/dialog-include/query-parameters"/>
                                                                <ctaNewWindow
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                                    checked="{Boolean}false"
                                                                    name="./ctaNewWindow"
                                                                    text="Open link in new tab"
                                                                    uncheckedValue="{Boolean}false"
                                                                    value="{Boolean}true"/>
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </ctaItems>
                                            <headingAppleStoreCta
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}3"
                                                text="Apple Store CTA"/>
                                            <appleStoreUrl
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Apple Store Url"
                                                name="./appleStoreUrl"
                                                rootPath="/content"/>
                                            <appleStoreIcon
                                                granite:class="cmp-image__editor-file-upload"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                allowUpload="{Boolean}false"
                                                class="cq-droptarget"
                                                fileNameParameter="./appleStoreCtaFileName"
                                                fileReferenceParameter="./appleStoreIcon"
                                                mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                name="./appleStoreCta"/>
                                            <appleStoreAlt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Apple Store Icon Alt"
                                                name="./appleStoreAlt"/>
                                            <appleStoreScript
                                                jcr:description="The JS Script that will be execute on click"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                fieldLabel="Custom Script"
                                                name="./appleStoreScript"
                                                resize="vertical"/>
                                            <headingGooglePlayCta
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}3"
                                                text="Google Play CTA"/>
                                            <googlePlayUrl
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                fieldLabel="Google Play Url"
                                                name="./googlePlayUrl"
                                                rootPath="/content"/>
                                            <googlePlayIcon
                                                granite:class="cmp-image__editor-file-upload"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                allowUpload="{Boolean}false"
                                                class="cq-droptarget"
                                                fileNameParameter="./googlePlayCtaFileName"
                                                fileReferenceParameter="./googlePlayIcon"
                                                mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                name="./googlePlayCta"/>
                                            <googlePlayAlt
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                fieldLabel="Google Play Icon Alt"
                                                name="./googlePlayAlt"/>
                                            <googlePlayScript
                                                jcr:description="The JS Script that will be execute on click"
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                fieldLabel="Custom Script"
                                                name="./googlePlayScript"
                                                resize="vertical"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabCta>
                    <tabAwards
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Awards"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <headingEGRAwards
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}4"
                                                text="EGR Awards"/>
                                            <egrAwardItems
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt-multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true">
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./egrAwardItems">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                            jcr:primaryType="nt:unstructured"
                                                            granite:class="cmp-image__editor-image-with-alt"
                                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <egrHeading
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                                    level="{Long}4"
                                                                    text="EGR Award Logo"/>
                                                                <egrAwardLogo
                                                                    granite:class="cmp-image__editor-file-upload"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                                    allowUpload="{Boolean}false"
                                                                    class="cq-droptarget"
                                                                    fileNameParameter="./awardImageName"
                                                                    fileReferenceParameter="./egrAwardLogo"
                                                                    mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                                    name="./awardImage"/>
                                                                <egrAwardAlt
                                                                    jcr:primaryType="nt:unstructured"
                                                                    granite:class="cmp-image__editor-alt-text"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Alt text"
                                                                    name="./egrAwardAlt"/>
                                                                <egrAwardImgTitle
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Image title attribute"
                                                                    name="./egrAwardImgTitle"/>
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </egrAwardItems>
                                            <headingOtherAwards
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}4"
                                                text="Other Awards"/>
                                            <otherAwardItems
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt-multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true">
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    granite:class="cmp-image__editor-image-with-alt"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./otherAwardItems">
                                                    <items jcr:primaryType="nt:unstructured">
                                                                <otherHeading
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                                    level="{Long}4"
                                                                    text="Award Logo"/>
                                                                <otherAwardLogo
                                                                    granite:class="cmp-image__editor-file-upload"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                                    allowUpload="{Boolean}false"
                                                                    class="cq-droptarget"
                                                                    fileReferenceParameter="./otherAwardLogo"
                                                                    required="{Boolean}true"
                                                                    mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                                    name="./awardImage"/>
                                                                <otherAwardAlt
                                                                    jcr:primaryType="nt:unstructured"
                                                                    granite:class="cmp-image__editor-alt-text"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Alt text"
                                                                    required="{Boolean}true"
                                                                    name="./otherAwardAlt"/>
                                                                <otherAwardImgTitle
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Image title attribute"
                                                                    name="./otherAwardImgTitle"/>
                                                    </items>
                                                </field>
                                            </otherAwardItems>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabAwards>
                    <tabPayments
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Payments"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <headingPayments
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}4"
                                                text="Payments"/>
                                            <paymentItems
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt-multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                validation="minmax-multifield">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    max-items="15"
                                                    min-items="0"
                                                    name="payment option"/>
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./paymentItems">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                            jcr:primaryType="nt:unstructured"
                                                            granite:class="cmp-image__editor-image-with-alt"
                                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <headingPaymentItem
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                                    level="{Long}3"
                                                                    text="Payment Item"/>
                                                                <logo
                                                                    granite:class="cmp-image__editor-file-upload"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                                    allowUpload="{Boolean}false"
                                                                    class="cq-droptarget"
                                                                    fileNameParameter="./paymentImageName"
                                                                    fileReferenceParameter="./logo"
                                                                    mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                                    name="./paymentName"/>
                                                                <gradientLogo
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/include"
                                                                    path="holdings888/components/common888/fragments/footer-v2/gradientLogo"/>
                                                                <alt
                                                                        jcr:primaryType="nt:unstructured"
                                                                        granite:class="cmp-image__editor-alt-text"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="Alt Text"
                                                                        name="./alt"/>
                                                                <title
                                                                        jcr:primaryType="nt:unstructured"
                                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                        fieldLabel="Image Title Attribute"
                                                                        name="./imgTitle"/>
                                                                <logolink
                                                                        jcr:primaryType="nt:unstructured"
                                                                        path="/apps/holdings888/components/common888/dialog-include/link"
                                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                                    <parameters
                                                                            jcr:primaryType="nt:unstructured"
                                                                            fieldsetTitle=" "
                                                                            linkUrlName="url"
                                                                            urlIsRequired="{Boolean}false"
                                                                            hideLabel="{Boolean}true"
                                                                            linkTitleName="title"
                                                                            hideScript="{Boolean}false"
                                                                            scriptName="script"
                                                                    />
                                                                </logolink>
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </paymentItems>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabPayments>
                    <tabRegulation
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Regulations"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <headingRegulations
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                level="{Long}3"
                                                text="Regulations"/>
                                            <regulationItems
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt-multifield"
                                                sling:resourceType="granite/ui/components/coral/foundation/form/multifield"
                                                composite="{Boolean}true"
                                                validation="minmax-multifield">
                                                <granite:data
                                                    jcr:primaryType="nt:unstructured"
                                                    max-items="15"
                                                    min-items="0"
                                                    name="regulation item"/>
                                                <field
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/container"
                                                    name="./regulationItems">
                                                    <items jcr:primaryType="nt:unstructured">
                                                        <column
                                                            jcr:primaryType="nt:unstructured"
                                                            granite:class="cmp-image__editor-image-with-alt"
                                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                                            <items jcr:primaryType="nt:unstructured">
                                                                <headingItems
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                                    level="{Long}4"
                                                                    text="Regulation Item"/>
                                                                <logo
                                                                    granite:class="cmp-image__editor-file-upload"
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                                    allowUpload="{Boolean}false"
                                                                    class="cq-droptarget"
                                                                    fileNameParameter="./logoName"
                                                                    fileReferenceParameter="./logo"
                                                                    mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                                    name="./regulationItem"/>
                                                                <gradientLogo
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/include"
                                                                    path="holdings888/components/common888/fragments/footer-v2/gradientLogo"/>
                                                                <alt
                                                                    jcr:primaryType="nt:unstructured"
                                                                    granite:class="cmp-image__editor-alt-text"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Alt Text"
                                                                    name="./alt"/>
                                                                <title
                                                                    jcr:primaryType="nt:unstructured"
                                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                                    fieldLabel="Image Title Attribute"
                                                                    name="./imgTitle"/>
                                                                <logolink
                                                                        jcr:primaryType="nt:unstructured"
                                                                        path="/apps/holdings888/components/common888/dialog-include/link"
                                                                        sling:resourceType="acs-commons/granite/ui/components/include">
                                                                    <parameters
                                                                            jcr:primaryType="nt:unstructured"
                                                                            fieldsetTitle=" "
                                                                            linkUrlName="url"
                                                                            urlIsRequired="{Boolean}false"
                                                                            hideLabel="{Boolean}true"
                                                                            linkTitleName="title"
                                                                            hideScript="{Boolean}false"
                                                                            scriptName="script"
                                                                    />
                                                                </logolink>
                                                            </items>
                                                        </column>
                                                    </items>
                                                </field>
                                            </regulationItems>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabRegulation>
                    <tabBanner
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Banner"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <leftBannerWrapper
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <headingLeftBanner
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                        level="{Long}3"
                                                        text="Left Banner"/>
                                                    <bannerLeftDesktop
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        allowUpload="{Boolean}false"
                                                        class="cq-droptarget"
                                                        fieldLabel="Banner Desktop"
                                                        fileNameParameter="./bannerImageName"
                                                        fileReferenceParameter="./bannerLeftDesktop"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./bannerLeftDesktopName"
                                                        useHTML5="{Boolean}true"/>
                                                    <bannerLeftMobile
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        allowUpload="{Boolean}false"
                                                        class="cq-droptarget"
                                                        fieldLabel="Banner Mobile"
                                                        fileNameParameter="./bannerImageMobileName"
                                                        fileReferenceParameter="./bannerLeftMobile"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./bannerLeftMobileName"
                                                        useHTML5="{Boolean}true"/>
                                                    <bannerLeftAlt
                                                        jcr:primaryType="nt:unstructured"
                                                        granite:class="cmp-image__editor-alt-text"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldLabel="Alt Text"
                                                        name="./bannerLeftAlt"/>
                                                    <bannerLeftTitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldLabel="Image Title Attribute"
                                                        name="./bannerLeftTitle"/>
                                                    <bannerLeftUrl
                                                        jcr:description="Enter the destination link"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldLabel="Banner Left Url"
                                                        name="./bannerLeftUrl"
                                                        rootPath="/content"/>
                                                    <bannerLeftScript
                                                        jcr:description="The JS Script that will be execute on click"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                        fieldLabel="Custom Script"
                                                        name="./bannerLeftScript"
                                                        resize="vertical"/>
                                                    <leftBlank
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        name="./leftBlank"
                                                        text="Open left link in new tab"
                                                        uncheckedValue="{Boolean}false"
                                                        value="{Boolean}true"/>
                                                </items>
                                            </leftBannerWrapper>
                                            <rightBannerWrapper
                                                jcr:primaryType="nt:unstructured"
                                                granite:class="cmp-image__editor-image-with-alt"
                                                sling:resourceType="granite/ui/components/coral/foundation/container">
                                                <items jcr:primaryType="nt:unstructured">
                                                    <headingRightBanner
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/heading"
                                                        level="{Long}3"
                                                        text="Right Banner"/>
                                                    <bannerRightDesktop
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        allowUpload="{Boolean}false"
                                                        class="cq-droptarget"
                                                        fieldLabel="Banner Desktop"
                                                        fileNameParameter="./bannerImageMobileName"
                                                        fileReferenceParameter="./bannerRightDesktop"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./bannerRightDesktopName"
                                                        useHTML5="{Boolean}true"/>
                                                    <bannerRightMobile
                                                        granite:class="cmp-image__editor-file-upload"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="cq/gui/components/authoring/dialog/fileupload"
                                                        allowUpload="{Boolean}false"
                                                        class="cq-droptarget"
                                                        fieldDescription="If not present, the desktop one will be used"
                                                        fieldLabel="Banner Mobile"
                                                        fileNameParameter="./bannerImageMobileName"
                                                        fileReferenceParameter="./bannerRightMobile"
                                                        mimeTypes="[image/gif,image/jpeg,image/png,image/tiff,image/svg+xml]"
                                                        name="./bannerRightMobileName"
                                                        useHTML5="{Boolean}true"/>
                                                    <bannerRightAlt
                                                        jcr:primaryType="nt:unstructured"
                                                        granite:class="cmp-image__editor-alt-text"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldLabel="Alt Text"
                                                        name="./bannerRightAlt"/>
                                                    <bannerRightTitle
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                        fieldLabel="Image Title Attribute"
                                                        name="./bannerRightTitle"/>
                                                    <bannerRightUrl
                                                        jcr:description="Enter the destination link"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/pathfield"
                                                        fieldLabel="Banner Right Url"
                                                        name="./bannerRightUrl"
                                                        rootPath="/content"/>
                                                    <bannerRightScript
                                                        jcr:description="The JS Script that will be execute on click"
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/textarea"
                                                        fieldLabel="Custom Script"
                                                        name="./bannerRightScript"
                                                        resize="vertical"/>
                                                    <rightBlank
                                                        jcr:primaryType="nt:unstructured"
                                                        sling:resourceType="granite/ui/components/coral/foundation/form/checkbox"
                                                        name="./rightBlank"
                                                        text="Open right link in new tab"
                                                        uncheckedValue="{Boolean}false"
                                                        value="{Boolean}true"/>
                                                </items>
                                            </rightBannerWrapper>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabBanner>
                    <tabDisclaimer
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Disclaimer"
                        sling:resourceType="granite/ui/components/coral/foundation/container"
                        margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <text
                                                jcr:primaryType="nt:unstructured"
                                                sling:resourceType="acs-commons/granite/ui/components/include"
                                                path="holdings888/components/dialog-include/rich-text"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabDisclaimer>
                    <tabTargetConfig
                            jcr:primaryType="nt:unstructured"
                            jcr:title="Target"
                            sling:resourceType="granite/ui/components/coral/foundation/container"
                            margin="{Boolean}true">
                        <items jcr:primaryType="nt:unstructured">
                            <columns
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="granite/ui/components/coral/foundation/fixedcolumns"
                                    margin="{Boolean}true">
                                <items jcr:primaryType="nt:unstructured">
                                    <column
                                            jcr:primaryType="nt:unstructured"
                                            sling:resourceType="granite/ui/components/coral/foundation/container">
                                        <items jcr:primaryType="nt:unstructured">
                                            <mboxId
                                                    jcr:primaryType="nt:unstructured"
                                                    sling:resourceType="granite/ui/components/coral/foundation/form/textfield"
                                                    fieldLabel="Target Mbox ID"
                                                    defaultValue="footer_mbox_id"
                                                    name="./mboxId"/>
                                        </items>
                                    </column>
                                </items>
                            </columns>
                        </items>
                    </tabTargetConfig>
                </items>
            </tabs>
        </items>
    </content>
</jcr:root>
